import { type PostInfo, getPostsAsJson } from './postDetection';
import { generateText, initializeOpenAI, getOpenAIService } from './openaiService';
import { loadOpenAIConfig } from './openaiConfig';
import { extractAndParseJSON } from './utils';

/**
 * 检测并处理网页中的文章列表
 * @param onProgress 进度回调函数，用于实时更新词云
 * @returns 返回处理后的关键词数据
 */
export async function processPagePosts(onProgress?: (keywords: any[]) => void) {
  try {
    console.log('[词云处理] 开始处理网页文章列表...');
    
    // 1. 获取网页中的文章列表
    const posts = getPostsAsJson();
    
    if (!posts || posts.length === 0) {
      console.log('[词云处理] 未检测到文章列表');
      return null;
    }
    
    console.log(`[词云处理] 检测到 ${posts.length} 篇文章`);
    
    // 2. 准备发送给OpenAI的数据 - 简化为仅包含id和title
    const simplifiedPosts = posts.map((post, index) => ({
      id: index + 1, // 添加自增ID
      title: post.title
    }));
    
    // 创建ID到完整帖子信息的映射，用于后续处理
    const fullPostsMap = new Map();
    posts.forEach((post, index) => {
      fullPostsMap.set(index + 1, {
        id: index + 1,
        title: post.title,
        link: post.link,
        author: post.author,
        time: post.time,
        xpath: post.xpath
      });
    });
    
    console.log('[词云处理] 准备的简化文章数据:', simplifiedPosts);
    
    // 3. 读取提示词模板
    const promptTemplate = await loadPromptTemplate();
    
    // 4. 替换占位符，生成完整提示词
    const fullPrompt = promptTemplate.replace('{{posts}}', JSON.stringify(simplifiedPosts, null, 2));
    console.log('[词云处理] 生成的完整提示词:', fullPrompt);
    
    // 5. 加载OpenAI配置并初始化服务
    const config = await loadOpenAIConfig();
    if (!config.apiKey) {
      console.error('[词云处理] 未配置OpenAI API密钥');
      throw new Error('未配置OpenAI API密钥，请在设置中配置');
    }
    
    // 初始化OpenAI服务
    console.log('[词云处理] 初始化OpenAI服务...');
    initializeOpenAI(config.apiKey, config.baseURL);
    
    console.log('[词云处理] 开始调用OpenAI流式API...');
    
    // 使用流式API处理
    let accumulatedResponse = '';
    let lastValidKeywords: any[] = [];
    let jsonStarted = false;
    let jsonBuffer = '';
    
    const response = await getOpenAIService().generateTextStreamWithLineProcessing(
      fullPrompt,
      config.model || 'gpt-3.5-turbo',
      (line: string, fullText: string) => {
        // 每收到完整的一行，尝试解析JSON
        accumulatedResponse = fullText;
        
        // 检测JSON开始标记
        if (line.includes('[') && !jsonStarted) {
          jsonStarted = true;
          jsonBuffer = line.substring(line.indexOf('['));
        } 
        // 如果已经开始收集JSON数据，继续追加
        else if (jsonStarted) {
          jsonBuffer += line;
        }

        // 尝试从当前累积的JSON缓冲区中提取和修复JSON
        if (jsonStarted) {
          // 首先尝试从累积的JSON缓冲区解析
          const bufferKeywords = parseOpenAIResponseWithRepair(jsonBuffer);
          if (bufferKeywords && Array.isArray(bufferKeywords) && bufferKeywords.length > 0) {
            // 检查是否有新的关键词数据
            if (JSON.stringify(bufferKeywords) !== JSON.stringify(lastValidKeywords)) {
              lastValidKeywords = bufferKeywords;
              
              // 为关键词添加完整帖子信息
              const enhancedKeywords = enhanceKeywordsWithPosts(bufferKeywords, fullPostsMap);
              
              // 调用进度回调，实时更新词云
              if (onProgress) {
                console.log('[词云处理] 实时更新关键词数据:', enhancedKeywords);
                onProgress(enhancedKeywords);
              }
            }
          }
        }

        // 同时也尝试从整个累积响应中解析，以防万一
        const currentKeywords = parseOpenAIResponseWithRepair(accumulatedResponse);
        if (currentKeywords && Array.isArray(currentKeywords) && currentKeywords.length > 0) {
          // 检查是否有新的关键词数据
          if (JSON.stringify(currentKeywords) !== JSON.stringify(lastValidKeywords)) {
            lastValidKeywords = currentKeywords;

            // 为关键词添加完整帖子信息
            const enhancedKeywords = enhanceKeywordsWithPosts(currentKeywords, fullPostsMap);

            // 调用进度回调，实时更新词云
            if (onProgress) {
              console.log('[词云处理] 实时更新关键词数据:', enhancedKeywords);
              onProgress(enhancedKeywords);
            }
          }
        }
      },
      config.maxTokens || 1000
    );
    
    // 6. 解析最终的OpenAI响应
    const keywordsData = parseOpenAIResponseWithRepair(response);
    console.log('[词云处理] 最终解析到的关键词数据:', keywordsData);

    // 如果最终解析失败，但之前有有效数据，使用最后的有效数据
    const finalKeywordsData = keywordsData || lastValidKeywords;

    if (!finalKeywordsData || !Array.isArray(finalKeywordsData) || finalKeywordsData.length === 0) {
      console.warn('[词云处理] 未能获取有效的关键词数据');
      return [];
    }

    // 7. 为每个关键词添加原始帖子完整信息
    const enhancedKeywordsData = enhanceKeywordsWithPosts(finalKeywordsData, fullPostsMap);

    console.log('[词云处理] 增强后的关键词数据:', enhancedKeywordsData);

    return enhancedKeywordsData;
  } catch (error) {
    console.error('[词云处理] 处理失败:', error);
    throw error;
  }
}

/**
 * 加载提示词模板
 */
async function loadPromptTemplate(): Promise<string> {
  try {
    const templateUrl = chrome.runtime.getURL('assets/prompt_parse.md');
    const response = await fetch(templateUrl);
    
    if (!response.ok) {
      throw new Error(`无法加载提示词模板: ${response.status}`);
    }
    
    const template = await response.text();
    return template;
  } catch (error) {
    console.error('[词云处理] 加载提示词模板失败:', error);
    throw error;
  }
}

/**
 * 解析OpenAI的响应，提取关键词数据（支持JSON修复）
 * @param response OpenAI的响应文本
 * @returns 关键词数据，解析失败时返回null
 */
function parseOpenAIResponseWithRepair(response: string): any {
  // 如果响应为空或太短，直接返回null
  if (!response || response.trim().length < 5) {
    return null;
  }

  try {
    // 首先尝试使用通用工具函数提取和解析JSON
    const parsedData = extractAndParseJSON(response, null);

    if (parsedData && Array.isArray(parsedData) && parsedData.length > 0) {
      // 验证关键词数据格式
      const validKeywords = parsedData.filter(item => 
        item && typeof item === 'object' && 
        item.keyword && 
        Array.isArray(item.ids) && 
        item.ids.length > 0
      );
      
      if (validKeywords.length > 0) {
        return validKeywords;
      }
    }

    // 如果通用解析没有结果，尝试使用OpenAI服务的JSON修复功能
    const service = getOpenAIService();
    const repairedData = service.tryParseAndRepairJSON(response);

    if (repairedData && Array.isArray(repairedData) && repairedData.length > 0) {
      // 验证关键词数据格式
      const validKeywords = repairedData.filter(item => 
        item && typeof item === 'object' && 
        item.keyword && 
        Array.isArray(item.ids) && 
        item.ids.length > 0
      );
      
      if (validKeywords.length > 0) {
        return validKeywords;
      }
    }

    // 如果以上方法都失败，尝试手动提取关键词数据
    const keywordMatches = response.match(/\"keyword\":\s*\"([^\"]+)\"/g);
    const idMatches = response.match(/\"ids\":\s*\[([\d,\s]+)\]/g);
    
    if (keywordMatches && idMatches && keywordMatches.length === idMatches.length) {
      const manualKeywords = [];
      
      for (let i = 0; i < keywordMatches.length; i++) {
        try {
          const keyword = keywordMatches[i].match(/\"([^\"]+)\"/)[1];
          const idsStr = idMatches[i].match(/\[([\d,\s]+)\]/)[1];
          const ids = idsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
          
          if (keyword && ids.length > 0) {
            manualKeywords.push({
              keyword,
              ids
            });
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
      
      if (manualKeywords.length > 0) {
        return manualKeywords;
      }
    }

    return null;
  } catch (error) {
    // 静默处理错误，避免控制台噪音
    return null;
  }
}

/**
 * 为关键词数据添加完整的帖子信息
 * @param keywordsData 关键词数据
 * @param fullPostsMap 完整帖子信息映射
 * @returns 增强后的关键词数据
 */
function enhanceKeywordsWithPosts(keywordsData: any[], fullPostsMap: Map<number, any>): any[] {
  if (!keywordsData || !Array.isArray(keywordsData)) {
    return [];
  }
  
  return keywordsData.map(keyword => {
    // 获取关联帖子的完整信息
    const originalPosts = keyword.ids?.map((id: number) => {
      // 使用完整帖子映射获取所有字段
      const fullPost = fullPostsMap.get(id);
      return fullPost || { id, title: `文章 #${id}` };
    }) || [];
    
    return {
      ...keyword,
      originalPosts
    };
  });
}

/**
 * 解析OpenAI的响应，提取关键词数据（兼容旧版本）
 * @param response OpenAI的响应文本
 * @returns 关键词数据
 */
function parseOpenAIResponse(response: string): any {
  return parseOpenAIResponseWithRepair(response);
}