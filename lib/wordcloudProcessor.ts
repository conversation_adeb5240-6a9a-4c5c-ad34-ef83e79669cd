import { type PostInfo, getPostsAsJson } from './postDetection';
import { generateText, initializeOpenAI, getOpenAIService } from './openaiService';
import { loadOpenAIConfig } from './openaiConfig';
import { extractAndParseJSON } from './utils';

/**
 * 检测并处理网页中的文章列表
 * @param onProgress 进度回调函数，用于实时更新词云
 * @returns 返回处理后的关键词数据
 */
export async function processPagePosts(onProgress?: (keywords: any[]) => void) {
  try {
    console.log('[词云处理] 开始处理网页文章列表...');
    
    // 1. 获取网页中的文章列表
    const posts = getPostsAsJson();
    
    if (!posts || posts.length === 0) {
      console.log('[词云处理] 未检测到文章列表');
      return null;
    }
    
    console.log(`[词云处理] 检测到 ${posts.length} 篇文章`);
    
    // 2. 准备发送给OpenAI的数据 - 简化为仅包含id和title
    const simplifiedPosts = posts.map((post, index) => ({
      id: index + 1, // 添加自增ID
      title: post.title
    }));
    
    // 创建ID到完整帖子信息的映射，用于后续处理
    const fullPostsMap = new Map();
    posts.forEach((post, index) => {
      fullPostsMap.set(index + 1, {
        id: index + 1,
        title: post.title,
        link: post.link,
        author: post.author,
        time: post.time,
        xpath: post.xpath
      });
    });
    
    console.log('[词云处理] 准备的简化文章数据:', simplifiedPosts);
    
    // 3. 读取提示词模板
    const promptTemplate = await loadPromptTemplate();
    
    // 4. 替换占位符，生成完整提示词
    const fullPrompt = promptTemplate.replace('{{posts}}', JSON.stringify(simplifiedPosts, null, 2));
    console.log('[词云处理] 生成的完整提示词:', fullPrompt);
    
    // 5. 加载OpenAI配置并初始化服务
    const config = await loadOpenAIConfig();
    if (!config.apiKey) {
      console.error('[词云处理] 未配置OpenAI API密钥');
      throw new Error('未配置OpenAI API密钥，请在设置中配置');
    }
    
    // 初始化OpenAI服务
    console.log('[词云处理] 初始化OpenAI服务...');
    initializeOpenAI(config.apiKey, config.baseURL);
    
    console.log('[词云处理] 开始调用OpenAI流式API...');
    
    // 使用流式API处理
    let accumulatedResponse = '';
    let lastValidKeywords: any[] = [];
    let jsonStarted = false;
    let jsonBuffer = '';
    
    // 使用更频繁的流式处理，不依赖完整行
    let updateTimeout: NodeJS.Timeout | null = null;

    await getOpenAIService().generateChatCompletionStream(
      [{ role: 'user', content: fullPrompt }],
      config.model || 'gpt-3.5-turbo',
      (chunk: string) => {
        // 每收到任何数据块，都累积到响应中
        accumulatedResponse += chunk;

        // 检测JSON开始标记
        if (chunk.includes('[') && !jsonStarted) {
          jsonStarted = true;
          const startIndex = accumulatedResponse.indexOf('[');
          if (startIndex !== -1) {
            jsonBuffer = accumulatedResponse.substring(startIndex);
          }
        }
        // 如果已经开始收集JSON数据，更新缓冲区
        else if (jsonStarted) {
          const startIndex = accumulatedResponse.indexOf('[');
          if (startIndex !== -1) {
            jsonBuffer = accumulatedResponse.substring(startIndex);
          }
        }

        // 立即尝试解析，如果有明显的新关键词就立即更新
        const tryImmediateUpdate = () => {
          if (jsonStarted && jsonBuffer) {
            const currentKeywords = parseOpenAIResponseWithRepair(jsonBuffer);
            if (currentKeywords && Array.isArray(currentKeywords) && currentKeywords.length > lastValidKeywords.length) {
              // 如果关键词数量明显增加，立即更新
              lastValidKeywords = [...currentKeywords];
              const enhancedKeywords = enhanceKeywordsWithPosts(currentKeywords, fullPostsMap);
              if (onProgress) {
                console.log('[词云处理] 立即更新关键词数据:', enhancedKeywords);
                onProgress(enhancedKeywords);
              }
              return true;
            }
          }
          return false;
        };

        // 尝试立即更新
        const immediateUpdated = tryImmediateUpdate();

        // 如果没有立即更新，使用防抖机制进行更细致的检查
        if (!immediateUpdated) {
          if (updateTimeout) {
            clearTimeout(updateTimeout);
          }

          updateTimeout = setTimeout(() => {
            let hasUpdate = false;

          // 尝试解析当前累积的数据
          if (jsonStarted && jsonBuffer) {
            const currentKeywords = parseOpenAIResponseWithRepair(jsonBuffer);
            if (currentKeywords && Array.isArray(currentKeywords) && currentKeywords.length > 0) {
              // 检查是否有新的关键词数据（比较数量和内容）
              if (currentKeywords.length > lastValidKeywords.length ||
                  currentKeywords.some((kw, index) =>
                    !lastValidKeywords[index] || kw.keyword !== lastValidKeywords[index].keyword
                  )) {
                lastValidKeywords = [...currentKeywords];
                hasUpdate = true;

                // 为关键词添加完整帖子信息
                const enhancedKeywords = enhanceKeywordsWithPosts(currentKeywords, fullPostsMap);

                // 调用进度回调，实时更新词云
                if (onProgress) {
                  console.log('[词云处理] 实时更新关键词数据 (缓冲区):', enhancedKeywords);
                  onProgress(enhancedKeywords);
                }
              }
            }
          }

          // 如果缓冲区没有更新，尝试从整个累积响应中解析
          if (!hasUpdate) {
            const allKeywords = parseOpenAIResponseWithRepair(accumulatedResponse);
            if (allKeywords && Array.isArray(allKeywords) && allKeywords.length > 0) {
              // 检查是否有新的关键词数据（比较数量和内容）
              if (allKeywords.length > lastValidKeywords.length ||
                  allKeywords.some((kw, index) =>
                    !lastValidKeywords[index] || kw.keyword !== lastValidKeywords[index].keyword
                  )) {
                lastValidKeywords = [...allKeywords];

                // 为关键词添加完整帖子信息
                const enhancedKeywords = enhanceKeywordsWithPosts(allKeywords, fullPostsMap);

                // 调用进度回调，实时更新词云
                if (onProgress) {
                  console.log('[词云处理] 实时更新关键词数据 (全文):', enhancedKeywords);
                  onProgress(enhancedKeywords);
                }
              }
            }
          }
        }, 150); // 减少到150ms防抖延迟，提高响应速度
        }
      },
      config.maxTokens || 1000
    );

    // 6. 解析最终的OpenAI响应
    const keywordsData = parseOpenAIResponseWithRepair(accumulatedResponse);
    console.log('[词云处理] 最终解析到的关键词数据:', keywordsData);

    // 如果最终解析失败，但之前有有效数据，使用最后的有效数据
    const finalKeywordsData = keywordsData || lastValidKeywords;

    if (!finalKeywordsData || !Array.isArray(finalKeywordsData) || finalKeywordsData.length === 0) {
      console.warn('[词云处理] 未能获取有效的关键词数据');
      return [];
    }

    // 7. 为每个关键词添加原始帖子完整信息
    const enhancedKeywordsData = enhanceKeywordsWithPosts(finalKeywordsData, fullPostsMap);

    console.log('[词云处理] 增强后的关键词数据:', enhancedKeywordsData);

    return enhancedKeywordsData;
  } catch (error) {
    console.error('[词云处理] 处理失败:', error);
    throw error;
  }
}

/**
 * 加载提示词模板
 */
async function loadPromptTemplate(): Promise<string> {
  try {
    const templateUrl = chrome.runtime.getURL('assets/prompt_parse.md');
    const response = await fetch(templateUrl);
    
    if (!response.ok) {
      throw new Error(`无法加载提示词模板: ${response.status}`);
    }
    
    const template = await response.text();
    return template;
  } catch (error) {
    console.error('[词云处理] 加载提示词模板失败:', error);
    throw error;
  }
}

/**
 * 解析OpenAI的响应，提取关键词数据（支持JSON修复）
 * @param response OpenAI的响应文本
 * @returns 关键词数据，解析失败时返回null
 */
function parseOpenAIResponseWithRepair(response: string): any {
  // 如果响应为空或太短，直接返回null
  if (!response || response.trim().length < 5) {
    return null;
  }

  try {
    // 首先尝试使用通用工具函数提取和解析JSON
    const parsedData = extractAndParseJSON(response, null);

    if (parsedData && Array.isArray(parsedData) && parsedData.length > 0) {
      // 验证关键词数据格式
      const validKeywords = parsedData.filter(item => 
        item && typeof item === 'object' && 
        item.keyword && 
        Array.isArray(item.ids) && 
        item.ids.length > 0
      );
      
      if (validKeywords.length > 0) {
        return validKeywords;
      }
    }

    // 如果通用解析没有结果，尝试使用OpenAI服务的JSON修复功能
    const service = getOpenAIService();
    const repairedData = service.tryParseAndRepairJSON(response);

    if (repairedData && Array.isArray(repairedData) && repairedData.length > 0) {
      // 验证关键词数据格式
      const validKeywords = repairedData.filter(item => 
        item && typeof item === 'object' && 
        item.keyword && 
        Array.isArray(item.ids) && 
        item.ids.length > 0
      );
      
      if (validKeywords.length > 0) {
        return validKeywords;
      }
    }

    // 如果以上方法都失败，尝试手动提取关键词数据
    const keywordMatches = response.match(/\"keyword\":\s*\"([^\"]+)\"/g);
    const idMatches = response.match(/\"ids\":\s*\[([\d,\s]+)\]/g);
    
    if (keywordMatches && idMatches && keywordMatches.length === idMatches.length) {
      const manualKeywords = [];
      
      for (let i = 0; i < keywordMatches.length; i++) {
        try {
          const keyword = keywordMatches[i].match(/\"([^\"]+)\"/)[1];
          const idsStr = idMatches[i].match(/\[([\d,\s]+)\]/)[1];
          const ids = idsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
          
          if (keyword && ids.length > 0) {
            manualKeywords.push({
              keyword,
              ids
            });
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
      
      if (manualKeywords.length > 0) {
        return manualKeywords;
      }
    }

    return null;
  } catch (error) {
    // 静默处理错误，避免控制台噪音
    return null;
  }
}

/**
 * 为关键词数据添加完整的帖子信息
 * @param keywordsData 关键词数据
 * @param fullPostsMap 完整帖子信息映射
 * @returns 增强后的关键词数据
 */
function enhanceKeywordsWithPosts(keywordsData: any[], fullPostsMap: Map<number, any>): any[] {
  if (!keywordsData || !Array.isArray(keywordsData)) {
    return [];
  }
  
  return keywordsData.map(keyword => {
    // 获取关联帖子的完整信息
    const originalPosts = keyword.ids?.map((id: number) => {
      // 使用完整帖子映射获取所有字段
      const fullPost = fullPostsMap.get(id);
      return fullPost || { id, title: `文章 #${id}` };
    }) || [];
    
    return {
      ...keyword,
      originalPosts
    };
  });
}

/**
 * 解析OpenAI的响应，提取关键词数据（兼容旧版本）
 * @param response OpenAI的响应文本
 * @returns 关键词数据
 */
function parseOpenAIResponse(response: string): any {
  return parseOpenAIResponseWithRepair(response);
}